import { Paper, Box, IconButton, Tooltip } from "@mui/material";
import { useEffect, useRef, useState, useCallback } from "react";
import RefreshIcon from '@mui/icons-material/Refresh';
import useProjectStore from "../../../store/Store";
import { invoke } from '@tauri-apps/api/core';
import './PreviewArea.css';
import { loadOperationsRecord, refreshPreview } from "../../../services/backendServer/BackendService";

// 添加一个自定义钩子来检测元素可见性
const useIsVisible = (ref: React.RefObject<HTMLElement>) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // 更新状态
        setIsVisible(entry.isIntersecting);
      },
      {
        root: null,
        threshold: 0.1, // 当至少10%的元素可见时
      }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [ref]);

  return isVisible;
};

// 声明全局变量类型
declare global {
  interface Window {
    SceneManager: any;
    Scene_Boot: any;
    Graphics: any;
  }
}

const PreviewArea = () => {
  const projectName = useProjectStore(state => state.projectName);
  const setGameWindow = useProjectStore(state => state.setGameWindow);
  console.log('项目路径:', projectName);

  // 引用容器元素
  const containerRef = useRef<HTMLDivElement>(null);

  // 添加状态
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // 检测组件是否可见
  const isVisible = useIsVisible(containerRef);

  // 设置游戏窗口到全局状态
  useEffect(() => {
    // 保存原始的Scene_Boot.start方法
    const _Scene_Boot_start = window.Scene_Boot.prototype.start;

    // 扩展Scene_Boot.start方法
    window.Scene_Boot.prototype.start = function () {
      // 调用原始方法
      _Scene_Boot_start.call(this);

      // // 加载操作记录
      setTimeout(async () => {
        try {
          console.log(`🔄 [ProjectSelector] 加载项目 的操作记录`);
          const loaded = await loadOperationsRecord();
          if (loaded) {
            console.log(`✅ [ProjectSelector] 项目  的操作记录加载成功`, loaded);
          } else {
            console.log(`ℹ️ [ProjectSelector] 项目  没有操作记录文件`);
          }
        } catch (error) {
          console.error(`❌ [ProjectSelector] 加载项目 的操作记录失败:`, error);
          // 不阻止项目打开，只是记录错误
        }
      }, 0);
      // 游戏加载完成后的自定义代码
      console.log("游戏加载完成！-----------------------");
      // 在这里添加你想要执行的代码
    };
    // 将全局window对象设置到Store中
    setGameWindow(window);
    console.log('游戏窗口已设置到Store中');
  }, [setGameWindow]);

  // 当组件可见性变化时，处理游戏循环
  useEffect(() => {
    if (window.SceneManager) {
      if (isVisible) {
        console.log('PreviewArea 可见，恢复游戏循环');
        // 恢复游戏循环
        if (window.Graphics && window.Graphics._app) {
          window.Graphics._app.start();

          // 显示游戏画面
          const gameCanvas = document.getElementById('gameCanvas');
          if (gameCanvas) {
            gameCanvas.style.opacity = '1';
            gameCanvas.style.filter = 'none';
          }
        }
      } else {
        console.log('PreviewArea 不可见，暂停游戏循环');
        // 暂停游戏循环
        if (window.Graphics && window.Graphics._app) {
          window.Graphics._app.stop();

          // 隐藏游戏画面但保持音频
          const gameCanvas = document.getElementById('gameCanvas');
          if (gameCanvas) {
            gameCanvas.style.opacity = '0';
          }
        }
      }
    }
  }, [isVisible]);

  // 处理刷新按钮点击
  const handleRefresh = useCallback(async () => {
    console.log('🔄 [PreviewArea] 手动刷新预览区域开始');
    setIsRefreshing(true);

    try {
      console.log('🔄 [PreviewArea] 准备调用 refreshPreview 函数');

      // 检查 refreshPreview 函数是否存在
      if (typeof refreshPreview !== 'function') {
        throw new Error('refreshPreview 不是一个函数');
      }

      console.log('🔄 [PreviewArea] refreshPreview 函数存在，开始调用');

      // 调用后端 API 将临时插件复制到项目目录
      const result = await refreshPreview();
      console.log('✅ [PreviewArea] 刷新结果:', result);

      // 刷新游戏
      if (window.SceneManager && window.Scene_Boot) {
        console.log('🎮 [PreviewArea] 重启游戏场景==========================');
        // window.SceneManager.goto(window.Scene_Boot);
        // 保存原始的Scene_Boot.start方法
        const _Scene_Boot_start = window.Scene_Boot.prototype.start;

        // 扩展Scene_Boot.start方法
        window.Scene_Boot.prototype.start = function () {
          // 调用原始方法
          _Scene_Boot_start.call(this);

          // // 加载操作记录
          // try {
          //   console.log(`🔄 [ProjectSelector] 加载项目 的操作记录`);
          //   const loaded = await loadOperationsRecord();
          //   if (loaded) {
          //     console.log(`✅ [ProjectSelector] 项目  的操作记录加载成功`, loaded);
          //   } else {
          //     console.log(`ℹ️ [ProjectSelector] 项目  没有操作记录文件`);
          //   }
          // } catch (error) {
          //   console.error(`❌ [ProjectSelector] 加载项目 的操作记录失败:`, error);
          //   // 不阻止项目打开，只是记录错误
          // }
          // 游戏加载完成后的自定义代码
          console.log("游戏加载完成！-----------------------");
          // 在这里添加你想要执行的代码
        };
      } else {
        console.log('⚠️ [PreviewArea] SceneManager 或 Scene_Boot 不可用');
      }

    } catch (error) {
      console.error('❌ [PreviewArea] 刷新预览失败:', error);
      console.error('❌ [PreviewArea] 错误详情:', error.stack);
    } finally {
      // 500ms 后重置刷新状态，显示视觉反馈
      setTimeout(() => {
        console.log('🔄 [PreviewArea] 重置刷新状态');
        setIsRefreshing(false);
      }, 500);
    }
  }, []);

  // 添加键盘快捷键支持 (F5 刷新)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // F5 键刷新预览
      if (e.key === 'F5') {
        e.preventDefault(); // 阻止浏览器默认刷新
        handleRefresh();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleRefresh]);

  // 将canvas移动到PreviewArea组件中
  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;

    // 创建一个定时器，定期检查并移动canvas元素
    // 这是为了确保即使在游戏引擎重新创建canvas时，我们也能捕获并移动它
    const moveCanvasInterval = setInterval(() => {
      // 查找已存在的canvas元素
      const gameCanvas = document.getElementById('gameCanvas');
      const errorPrinter = document.getElementById('errorPrinter');
      const fpsCounterBox = document.getElementById('fpsCounterBox');
      const gameVideo = document.getElementById('gameVideo');

      // 查找或创建游戏容器
      let gameContainer = container.querySelector('.game-container') as HTMLElement;
      if (!gameContainer) {
        gameContainer = document.createElement('div');
        gameContainer.className = 'game-container';
        container.appendChild(gameContainer);
      }

      // 移动canvas到游戏容器中
      if (gameCanvas && gameCanvas.parentNode !== gameContainer) {
        // 修复canvas样式
        gameCanvas.style.opacity = '1';
        gameCanvas.style.filter = 'none';

        // 如果canvas不在游戏容器中，移动它
        try {
          if (gameCanvas.parentNode) {
            gameCanvas.parentNode.removeChild(gameCanvas);
          }
          gameContainer.appendChild(gameCanvas);
          console.log('Canvas已移动到PreviewArea组件中');
        } catch (e) {
          console.error('移动Canvas时出错:', e);
        }
      }

      // 移动其他游戏元素
      if (errorPrinter && errorPrinter.parentNode !== gameContainer) {
        try {
          if (errorPrinter.parentNode) {
            errorPrinter.parentNode.removeChild(errorPrinter);
          }
          gameContainer.appendChild(errorPrinter);

          // 隐藏错误提示
          errorPrinter.style.display = 'none';
        } catch (e) {
          console.error('移动errorPrinter时出错:', e);
        }
      }

      if (fpsCounterBox && fpsCounterBox.parentNode !== gameContainer) {
        try {
          if (fpsCounterBox.parentNode) {
            fpsCounterBox.parentNode.removeChild(fpsCounterBox);
          }
          gameContainer.appendChild(fpsCounterBox);
        } catch (e) {
          console.error('移动fpsCounterBox时出错:', e);
        }
      }

      if (gameVideo && gameVideo.parentNode !== gameContainer) {
        try {
          if (gameVideo.parentNode) {
            gameVideo.parentNode.removeChild(gameVideo);
          }
          gameContainer.appendChild(gameVideo);
        } catch (e) {
          console.error('移动gameVideo时出错:', e);
        }
      }
    }, 100); // 每100毫秒检查一次

    // 添加右键菜单事件处理
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      handleRefresh();
      return false;
    };

    container.addEventListener('contextmenu', handleContextMenu);

    // 清理函数
    return () => {
      // 清除定时器
      clearInterval(moveCanvasInterval);

      // 移除事件监听器
      container.removeEventListener('contextmenu', handleContextMenu);

      // 查找游戏容器
      const gameContainer = container.querySelector('.game-container') as HTMLElement;
      if (!gameContainer) return;

      // 将游戏元素移回body
      const gameCanvas = document.getElementById('gameCanvas');
      const errorPrinter = document.getElementById('errorPrinter');
      const fpsCounterBox = document.getElementById('fpsCounterBox');
      const gameVideo = document.getElementById('gameVideo');

      // 确保游戏循环继续运行，但canvas可能不可见
      if (window.Graphics && window.Graphics._app) {
        // 恢复游戏循环
        window.Graphics._app.start();

        // 确保canvas可见
        if (gameCanvas) {
          gameCanvas.style.opacity = '1';
          gameCanvas.style.filter = 'none';
        }
      }

      if (gameCanvas && gameCanvas.parentNode === gameContainer) {
        try {
          document.body.appendChild(gameCanvas);
        } catch (e) {
          console.error('移回gameCanvas时出错:', e);
        }
      }

      if (errorPrinter && errorPrinter.parentNode === gameContainer) {
        try {
          document.body.appendChild(errorPrinter);
        } catch (e) {
          console.error('移回errorPrinter时出错:', e);
        }
      }

      if (fpsCounterBox && fpsCounterBox.parentNode === gameContainer) {
        try {
          document.body.appendChild(fpsCounterBox);
        } catch (e) {
          console.error('移回fpsCounterBox时出错:', e);
        }
      }

      if (gameVideo && gameVideo.parentNode === gameContainer) {
        try {
          document.body.appendChild(gameVideo);
        } catch (e) {
          console.error('移回gameVideo时出错:', e);
        }
      }

      // 移除游戏容器
      if (gameContainer.parentNode === container) {
        container.removeChild(gameContainer);
      }
    };
  }, [handleRefresh]);

  return (
    <Paper
      elevation={3}
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
      }}
    >
      {/* 添加工具栏 */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          p: 0.5,
          borderBottom: '1px solid #eee',
          bgcolor: '#f5f5f5'
        }}
      >
        <Tooltip title="刷新预览 (F5) - 手动刷新以查看最新更改">
          <IconButton
            onClick={handleRefresh}
            size="small"
            color={isRefreshing ? "primary" : "default"}
            sx={{
              transition: 'all 0.3s',
              animation: isRefreshing ? 'spin 0.5s linear' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* 游戏容器 */}
      <Box
        ref={containerRef}
        sx={{
          flex: 1,
          width: '100%',
          height: '100%',
          position: 'relative',
          bgcolor: 'black',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      />
    </Paper>
  );
};

export default PreviewArea;
